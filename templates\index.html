<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tushare数据镜像系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-database"></i>
                Tushare数据镜像系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showDatabaseStatus()">
                    <i class="bi bi-info-circle"></i>
                    数据库状态
                </a>
                <a class="nav-link" href="https://tushare.pro/" target="_blank">
                    <i class="bi bi-box-arrow-up-right"></i>
                    Tushare官网
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 系统状态概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-database fs-1 text-primary"></i>
                        <h5 class="card-title">数据表</h5>
                        <p class="card-text fs-4" id="totalTables">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-check-circle fs-1 text-success"></i>
                        <h5 class="card-title">已同步</h5>
                        <p class="card-text fs-4" id="syncedTables">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-collection fs-1 text-info"></i>
                        <h5 class="card-title">总记录数</h5>
                        <p class="card-text fs-4" id="totalRecords">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-activity fs-1 text-warning"></i>
                        <h5 class="card-title">同步状态</h5>
                        <p class="card-text fs-4" id="syncStatus">待机</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据同步控制区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-arrow-repeat"></i>
                            数据同步控制
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="tableSelect" class="form-label">选择数据表</label>
                                <select class="form-select" id="tableSelect">
                                    <option value="all">所有表</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="startDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-2">
                                <label for="endDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-2">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="forceFull">
                                    <label class="form-check-label" for="forceFull">
                                        强制全量同步
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button class="btn btn-success" id="startSync">
                                        <i class="bi bi-arrow-repeat"></i>
                                        开始同步
                                    </button>
                                    <button class="btn btn-danger d-none" id="stopSync">
                                        <i class="bi bi-stop-fill"></i>
                                        停止同步
                                    </button>
                                    <button class="btn btn-info ms-2" id="refreshStatus">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        刷新状态
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 同步进度显示区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-activity"></i>
                            同步进度
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3" style="height: 25px;">
                            <div class="progress-bar progress-bar-striped" role="progressbar"
                                 style="width: 0%" id="progressBar">0%</div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>当前状态：</strong>
                                <span id="currentStatus" class="text-muted">等待开始</span>
                            </div>
                            <div class="col-md-3">
                                <strong>当前表：</strong>
                                <span id="currentTable">-</span>
                            </div>
                            <div class="col-md-3">
                                <strong>已完成表：</strong>
                                <span id="completedTables">0</span> / <span id="totalTablesSync">0</span>
                            </div>
                            <div class="col-md-3">
                                <strong>总记录数：</strong>
                                <span id="totalRecordsSync">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 同步日志区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-journal-text"></i>
                            同步日志
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="log-container" id="logContainer" style="max-height: 400px; overflow-y: auto;">
                            <div class="text-muted">等待开始同步...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <!-- 数据镜像系统JS -->
    <script src="{{ url_for('static', filename='mirror.js') }}"></script>
</body>
</html>
