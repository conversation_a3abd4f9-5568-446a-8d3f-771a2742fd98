from flask import Flask, render_template, request, jsonify, send_file
from flask_socketio import Socket<PERSON>, emit
import pandas as pd
from datetime import datetime, timedelta
import time
import os
import threading
import json
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import get_config, TUSHARE_TABLES
from database.models import db_config, SyncStatus
from sync.data_sync import TushareDataSync

app = Flask(__name__)
config = get_config()
app.config['SECRET_KEY'] = config.SECRET_KEY
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
sync_status = {
    'is_running': False,
    'current_table': '',
    'current_segment': 0,
    'total_segments': 0,
    'total_records': 0,
    'progress': 0,
    'status': 'waiting',
    'error': None,
    'tables_completed': 0,
    'total_tables': 0
}

# 初始化数据同步器
syncer = TushareDataSync()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/start_sync', methods=['POST'])
def start_sync():
    global sync_status

    if sync_status['is_running']:
        return jsonify({'error': '同步正在进行中'}), 400

    data = request.json
    table_name = data.get('table_name', 'all')
    start_date = data.get('start_date')
    end_date = data.get('end_date')
    force_full = data.get('force_full', False)

    # 重置状态
    sync_status.update({
        'is_running': True,
        'current_table': table_name,
        'current_segment': 0,
        'total_segments': 0,
        'total_records': 0,
        'progress': 0,
        'status': 'starting',
        'error': None,
        'tables_completed': 0,
        'total_tables': 1 if table_name != 'all' else len(TUSHARE_TABLES)
    })

    # 在后台线程中开始同步
    thread = threading.Thread(target=sync_data_background,
                             args=(table_name, start_date, end_date, force_full))
    thread.daemon = True
    thread.start()

    return jsonify({'message': '数据同步已开始'})

@app.route('/api/stop_sync', methods=['POST'])
def stop_sync():
    global sync_status
    sync_status['is_running'] = False
    sync_status['status'] = 'stopped'

    socketio.emit('sync_status', sync_status)
    return jsonify({'message': '同步已停止'})

@app.route('/api/sync_status')
def get_sync_status():
    return jsonify(sync_status)

@app.route('/api/tables')
def get_tables():
    """获取支持的数据表列表"""
    tables = []
    for table_name, config in TUSHARE_TABLES.items():
        tables.append({
            'name': table_name,
            'chinese_name': config['name'],
            'sync_type': config['sync_type'],
            'sync_frequency': config['sync_frequency']
        })
    return jsonify(tables)

@app.route('/api/database_status')
def get_database_status():
    """获取数据库状态"""
    try:
        session = db_config.get_session()

        # 获取各表记录数
        tables_status = []
        for table_name in TUSHARE_TABLES.keys():
            try:
                from sqlalchemy import text
                result = session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                count = result.scalar()
                tables_status.append({
                    'table_name': table_name,
                    'chinese_name': TUSHARE_TABLES[table_name]['name'],
                    'record_count': count
                })
            except Exception as e:
                tables_status.append({
                    'table_name': table_name,
                    'chinese_name': TUSHARE_TABLES[table_name]['name'],
                    'record_count': 0,
                    'error': str(e)
                })

        # 获取同步状态
        sync_statuses = session.query(SyncStatus).all()
        sync_info = {}
        for status in sync_statuses:
            sync_info[status.table_name] = {
                'last_sync_date': status.last_sync_date,
                'last_sync_time': status.last_sync_time.isoformat() if status.last_sync_time else None,
                'sync_status': status.sync_status,
                'error_message': status.error_message
            }

        session.close()

        return jsonify({
            'tables': tables_status,
            'sync_info': sync_info
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/download_history')
def get_download_history():
    # 从文件或数据库读取下载历史
    history_file = 'download_history.json'
    if os.path.exists(history_file):
        with open(history_file, 'r', encoding='utf-8') as f:
            history = json.load(f)
    else:
        history = []
    
    return jsonify(history)

@app.route('/api/download_file/<filename>')
def download_file(filename):
    try:
        return send_file(filename, as_attachment=True)
    except FileNotFoundError:
        return jsonify({'error': '文件不存在'}), 404

def sync_data_background(table_name, start_date, end_date, force_full):
    """后台同步数据"""
    global sync_status

    try:
        sync_status['status'] = 'syncing'

        if table_name == 'all':
            # 同步所有表
            socketio.emit('sync_log', {
                'message': f'开始同步所有数据表',
                'type': 'info'
            })

            tables_to_sync = list(TUSHARE_TABLES.keys())
            sync_status['total_tables'] = len(tables_to_sync)

            for i, table in enumerate(tables_to_sync, 1):
                if not sync_status['is_running']:
                    break

                sync_status['current_table'] = table
                sync_status['tables_completed'] = i - 1
                sync_status['progress'] = ((i - 1) / len(tables_to_sync)) * 100

                socketio.emit('sync_status', sync_status)
                socketio.emit('sync_log', {
                    'message': f'正在同步表 {table} ({TUSHARE_TABLES[table]["name"]}) [{i}/{len(tables_to_sync)}]',
                    'type': 'info'
                })

                try:
                    result = syncer.sync_table(table, start_date, end_date, force_full)

                    if result['status'] == 'success':
                        sync_status['total_records'] += result['record_count']
                        socketio.emit('sync_log', {
                            'message': f'✓ 表 {table} 同步完成: {result["record_count"]:,} 条记录',
                            'type': 'success'
                        })
                    else:
                        socketio.emit('sync_log', {
                            'message': f'✗ 表 {table} 同步失败: {result.get("error_message", "未知错误")}',
                            'type': 'error'
                        })

                except Exception as e:
                    socketio.emit('sync_log', {
                        'message': f'✗ 表 {table} 同步异常: {str(e)}',
                        'type': 'error'
                    })

                time.sleep(1)  # 表间休息

            sync_status['tables_completed'] = len(tables_to_sync)
            sync_status['progress'] = 100

        else:
            # 同步单个表
            socketio.emit('sync_log', {
                'message': f'开始同步表 {table_name} ({TUSHARE_TABLES.get(table_name, {}).get("name", "未知")})',
                'type': 'info'
            })

            try:
                result = syncer.sync_table(table_name, start_date, end_date, force_full)

                if result['status'] == 'success':
                    sync_status['total_records'] = result['record_count']
                    sync_status['progress'] = 100
                    socketio.emit('sync_log', {
                        'message': f'✓ 表 {table_name} 同步完成: {result["record_count"]:,} 条记录',
                        'type': 'success'
                    })
                else:
                    sync_status['error'] = result.get('error_message', '未知错误')
                    socketio.emit('sync_log', {
                        'message': f'✗ 表 {table_name} 同步失败: {sync_status["error"]}',
                        'type': 'error'
                    })

            except Exception as e:
                sync_status['error'] = str(e)
                socketio.emit('sync_log', {
                    'message': f'✗ 表 {table_name} 同步异常: {str(e)}',
                    'type': 'error'
                })

        if sync_status['is_running']:
            sync_status['status'] = 'completed'
            socketio.emit('sync_log', {
                'message': f'数据同步完成！总计 {sync_status["total_records"]:,} 条记录',
                'type': 'success'
            })

    except Exception as e:
        sync_status['error'] = str(e)
        sync_status['status'] = 'error'
        socketio.emit('sync_log', {
            'message': f'同步过程发生错误：{str(e)}',
            'type': 'error'
        })

    finally:
        sync_status['is_running'] = False
        socketio.emit('sync_status', sync_status)

@socketio.on('connect')
def handle_connect():
    """处理WebSocket连接"""
    emit('sync_status', sync_status)

def save_download_history(start_date, end_date, record_count, filename):
    """保存下载历史"""
    history_file = 'download_history.json'
    
    # 读取现有历史
    if os.path.exists(history_file):
        with open(history_file, 'r', encoding='utf-8') as f:
            history = json.load(f)
    else:
        history = []
    
    # 添加新记录
    new_record = {
        'download_time': datetime.now().isoformat(),
        'start_date': start_date,
        'end_date': end_date,
        'record_count': record_count,
        'filename': filename,
        'file_size': os.path.getsize(filename) if os.path.exists(filename) else 0,
        'status': 'completed'
    }
    
    history.insert(0, new_record)  # 最新的在前面
    
    # 只保留最近50条记录
    history = history[:50]
    
    # 保存历史
    with open(history_file, 'w', encoding='utf-8') as f:
        json.dump(history, f, ensure_ascii=False, indent=2)

if __name__ == '__main__':
    # 确保必要的目录存在
    from config import ensure_directories
    ensure_directories()

    # 创建templates目录并移动HTML文件
    os.makedirs('templates', exist_ok=True)
    if os.path.exists('index.html'):
        import shutil
        shutil.move('index.html', 'templates/index.html')

    print("启动Tushare数据镜像系统...")
    print(f"Web界面: http://localhost:{config.WEB_CONFIG['port']}")

    socketio.run(app,
                debug=config.WEB_CONFIG['debug'],
                host=config.WEB_CONFIG['host'],
                port=config.WEB_CONFIG['port'])
